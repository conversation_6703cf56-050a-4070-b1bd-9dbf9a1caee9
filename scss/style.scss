/******* Bootstrap Theming ********/

$primary: #E31C25;
$secondary: #111111;
$light: #ffffff;

$theme-colors: (
    "primary": $primary,
    "secondary": $secondary,
    "light": $light,
);

$font-family-sans-serif: '<PERSON><PERSON><PERSON>',
sans-serif;

$headings-font-family: '<PERSON>',
sans-serif;

$body-bg: $light;

$body-color: #666666;

$headings-color: #343a40;

$btn-border-width: 2px;

$enable-responsive-font-sizes: true;

$enable-rounded: false;

$enable-shadows: false;

@import "https://fonts.googleapis.com/css2?family=Montserrat&family=Oswald&display=swap";

@import "bootstrap/scss/bootstrap";


/********** Custom CSS ************/
[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
    font-size: inherit;
    margin-left: 0;
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 11;
    -webkit-animation: action 1s infinite alternate;
    animation: action 1s infinite alternate;
}

@-webkit-keyframes action {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-15px);
    }
}

@keyframes action {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-15px);
    }
}

.container-fluid.nav-bar {
    position: absolute;
    z-index: 9;
}

@media (max-width: 991.98px) {
    .container-fluid.nav-bar {
        position: relative;
        background: $secondary;
    }
}

.navbar-dark .navbar-nav .nav-link {
    padding-left: 15px;
    padding-right: 15px;
    font-weight: bold;
    letter-spacing: 1px;
    color: $light;
  }
  
  .navbar-dark .navbar-nav .nav-link:hover,
  .navbar-dark .navbar-nav .nav-link.active {
    color: $primary;
  }

.carousel-caption {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .3);
    z-index: 1;
}

.gym-class {
    position: relative;
    margin-top: 90px;
}

.gym-class .gym-class-box {
    min-height: 450px;
}

@media (min-width: 992px) {
    .gym-class {
        margin-top: -90px;
        z-index: 1;
    }
}

.gym-class i {
    position: absolute;
    font-size: 200px;
    line-height: 200px;
    bottom: 0;
    color: $light;
    opacity: .1;
}

.gym-class .gym-class-box.text-right i {
    left: 0;
}

.gym-class .gym-class-box.text-left i {
    right: 0;
}

.feature i {
    position: absolute;
    top: -15px;
    right: 0px;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 35px;
    color: $white;
    background: $primary;
}

@media (max-width: 767.98px) {
    .bmi {
      background: $secondary;
    }
}
  
@media (min-width: 768px) {
    .bmi::after {
      position: absolute;
      content: "";
      width: 50%;
      height: 100%;
      top: 0;
      left: 0;
      background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, .5)), url(../img/carousel-1.jpg);
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      z-index: -1;
    }
}

.team .card-social {
    position: absolute;
    width: 100%;
    height: 0;
    top: 100%;
    left: 0;
    opacity: 0;
    transition: .5s;
    z-index: 1;
}

.team .card:hover .card-social {
    top: 0;
    height: calc(100% - 100px);
    opacity: 1;
    background: rgba(0, 0, 0, 0.3);
}

.team .card-body {
    position: relative;
    z-index: 2;
}

@media (max-width: 767.98px) {
    .testimonial {
      background: $secondary;
    }
}
  
@media (min-width: 768px) {
    .testimonial::after {
      position: absolute;
      content: "";
      width: 50%;
      height: 100%;
      top: 0;
      right: 0;
      background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, .5)), url(../img/carousel-2.jpg);
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      z-index: -1;
    }
}

.testimonial-text::before {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    top: -20px;
    left: 30px;
    border: 10px solid;
    border-color: transparent transparent #343a40 transparent;
    z-index: 2;
}

.testimonial-text::after {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    top: -24px;
    left: 28px;
    border: 12px solid;
    border-color: transparent transparent #dee2e6 transparent;
    z-index: 1;
}

.contact-form .help-block ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.page-header,
.subscribe,
.footer {
  background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url(../img/bg.jpg);
  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;
}